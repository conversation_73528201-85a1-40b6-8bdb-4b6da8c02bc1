services:
  db:
    image: mysql:8.0
    container_name: physics_db
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_PASSWORD}
      MYSQL_DATABASE: ${DB_NAME}
      MYSQL_CHARSET: utf8mb4
      MYSQL_COLLATION: utf8mb4_unicode_ci
    command: --character-set-server=utf8mb4 --collation-server=utf8mb4_unicode_ci --default-authentication-plugin=mysql_native_password
    ports:
      - "${DB_EXTERNAL_PORT}:3306"
    volumes:
      - /vdb_experiments:/var/lib/mysql
      - ./database/init.sql:/docker-entrypoint-initdb.d/init.sql:ro
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-p${DB_PASSWORD}"]
      interval: 5s
      timeout: 5s
      retries: 20

  backend:
    build:
      context: .
      dockerfile: backend/Dockerfile
    container_name: physics_backend
    depends_on:
      db:
        condition: service_healthy
    env_file: .env
    environment:
      # Ensure backend connects to the Docker DB service
      DB_HOST: db
      # Force using MySQL (disable default SQLite)
      DATABASE_URL: ""
    ports:
      - "${API_EXTERNAL_PORT}:8000"
    volumes:
      - ./backend:/app/backend
      - ./backend/logs:/app/backend/logs
      - ./backend/uploads:/app/backend/uploads
    working_dir: /app/backend
    command: uvicorn app.main:app --host 0.0.0.0 --port 8000 --reload

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: physics_frontend
    depends_on:
      - backend
    ports:
      - "${FRONTEND_EXTERNAL_PORT}:80"
    environment:
      - VITE_API_BASE_URL=http://localhost:${API_EXTERNAL_PORT}

  admin:
    build:
      context: ./admin
      dockerfile: Dockerfile
    container_name: physics_admin
    depends_on:
      - backend
    ports:
      - "${ADMIN_EXTERNAL_PORT}:80"
    environment:
      - VITE_API_BASE_URL=

# No named volumes needed - using host paths

