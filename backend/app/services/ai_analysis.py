"""
AI分析服务
"""

import httpx
import json
from typing import Dict, Any, Tuple, Optional
import logging

from app.utils.config import settings

logger = logging.getLogger(__name__)


class AIAnalysisService:
    """AI分析服务类"""
    
    def __init__(self):
        self.ai_enabled = settings.AI_ANALYSIS_ENABLED
        self.ai_service_url = settings.AI_SERVICE_URL
        self.ai_api_key = settings.AI_SERVICE_API_KEY
        
        self.analyzers = {
            'current_control_circuit': self._analyze_current_control_circuit,
            'oscilloscope': self._analyze_oscilloscope,
        }
    
    def analyze_experiment(
        self,
        experiment_code: str,
        data: Dict[str, Any]
    ) -> Tuple[str, Optional[bool]]:
        """分析实验数据"""
        try:
            # 为前端联调提供稳定的模拟结果：直接返回“通过”
            if not self.ai_enabled:
                return "模拟分析：已接收数据，实验通过", True

            if experiment_code not in self.analyzers:
                # 未注册的实验类型也返回模拟通过，便于测试
                return "模拟分析：实验类型未注册，但已通过（测试模式）", True

            analyzer = self.analyzers[experiment_code]
            analysis_result, is_passed = analyzer(data)

            return analysis_result, is_passed

        except Exception as e:
            logger.error(f"AI分析失败: {e}")
            return f"AI分析出现错误: {str(e)}", None

    def analyze_experiment_data(
        self,
        experiment_code: str,
        data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """分析实验数据（返回字典格式）"""
        try:
            analysis_result, is_passed = self.analyze_experiment(experiment_code, data)
            return {
                "message": analysis_result,
                "is_passed": is_passed if is_passed is not None else True
            }
        except Exception as e:
            logger.error(f"AI分析失败: {e}")
            return {
                "message": f"AI分析出现错误: {str(e)}",
                "is_passed": False
            }
    
    def _analyze_current_control_circuit(self, data: Dict[str, Any]) -> Tuple[str, bool]:
        """分析制流电路实验数据"""
        try:
            k1_current = data.get('k1_current', [])
            k01_current = data.get('k01_current', [])
            
            if not k1_current or not k01_current:
                return "缺少必要的电流数据", False
            
            if len(k1_current) != 11 or len(k01_current) != 11:
                return "电流数据点数不正确，应为11个数据点", False
            
            # 基本数据验证
            analysis_points = []
            is_passed = True
            
            # 检查k=1时的电流变化趋势
            k1_trend_correct = self._check_current_trend(k1_current, "k=1")
            if k1_trend_correct:
                analysis_points.append("✓ k=1时电流变化趋势正确")
            else:
                analysis_points.append("✗ k=1时电流变化趋势异常")
                is_passed = False
            
            # 检查k=0.1时的电流变化趋势
            k01_trend_correct = self._check_current_trend(k01_current, "k=0.1")
            if k01_trend_correct:
                analysis_points.append("✓ k=0.1时电流变化趋势正确")
            else:
                analysis_points.append("✗ k=0.1时电流变化趋势异常")
                is_passed = False
            
            # 检查初始电流值是否相等
            initial_current_equal = abs(k1_current[0] - k01_current[0]) < 0.1
            if initial_current_equal:
                analysis_points.append("✓ 初始电流值(接入比例=0)基本相等")
            else:
                analysis_points.append("✗ 初始电流值差异过大")
                is_passed = False
            
            # 检查数据合理性
            data_reasonable = self._check_data_reasonableness(k1_current, k01_current)
            if data_reasonable:
                analysis_points.append("✓ 数据数值在合理范围内")
            else:
                analysis_points.append("✗ 数据数值异常")
                is_passed = False
            
            # 生成分析报告
            result = "## 制流电路实验数据分析\n\n"
            result += "### 分析结果:\n"
            for point in analysis_points:
                result += f"- {point}\n"
            
            result += f"\n### 总体评价:\n"
            if is_passed:
                result += "**实验通过** ✓\n\n"
                result += "实验数据符合制流电路的理论预期，电流变化趋势正确，数据质量良好。"
            else:
                result += "**实验未通过** ✗\n\n"
                result += "实验数据存在问题，请检查实验操作和数据记录是否正确。"
            
            return result, is_passed
            
        except Exception as e:
            logger.error(f"分析制流电路数据失败: {e}")
            return f"分析过程中出现错误: {str(e)}", False
    
    def _analyze_oscilloscope(self, data: Dict[str, Any]) -> Tuple[str, bool]:
        """分析示波器实验数据"""
        # 这里是示波器实验的分析逻辑
        # 暂时返回一个简单的分析结果
        return "示波器实验分析功能正在开发中", True
    
    def _check_current_trend(self, current_data: list, k_value: str) -> bool:
        """检查电流变化趋势"""
        try:
            # 制流电路的电流应该随接入比例增加而减少
            # 这里做简单的趋势检查
            decreasing_count = 0
            for i in range(1, len(current_data)):
                if current_data[i] <= current_data[i-1]:
                    decreasing_count += 1
            
            # 如果大部分数据点都呈递减趋势，认为趋势正确
            return decreasing_count >= len(current_data) * 0.7
            
        except Exception:
            return False
    
    def _check_data_reasonableness(self, k1_current: list, k01_current: list) -> bool:
        """检查数据合理性"""
        try:
            # 检查数据是否在合理范围内
            all_currents = k1_current + k01_current
            
            # 电流值应该为正数且在合理范围内(0-1000mA)
            for current in all_currents:
                if current < 0 or current > 1000:
                    return False
            
            return True
            
        except Exception:
            return False
