"""
实验服务
"""

from sqlalchemy.orm import Session
from typing import List, Optional, Dict, Any
from datetime import datetime
import json
import logging

from app.models.experiment import ExperimentType, ExperimentRecord, ExperimentStatus
from app.models.user import Student, UnassignedStudent
from app.services.plot import PlotService
from app.services.ai_analysis import AIAnalysisService

logger = logging.getLogger(__name__)


class ExperimentService:
    """实验服务类"""
    
    def __init__(self, db: Session):
        self.db = db
        self.plot_service = PlotService()
        self.ai_service = AIAnalysisService()
    
    def get_active_experiment_types(self) -> List[ExperimentType]:
        """获取所有激活的实验类型"""
        return self.db.query(ExperimentType).filter(
            ExperimentType.is_active == True
        ).all()
    
    def get_experiment_type_by_code(self, code: str) -> Optional[ExperimentType]:
        """根据代码获取实验类型"""
        return self.db.query(ExperimentType).filter(
            ExperimentType.code == code,
            ExperimentType.is_active == True
        ).first()
    
    def submit_experiment(
        self,
        student_id: int,
        experiment_code: str,
        submission_data: Dict[str, Any]
    ) -> ExperimentRecord:
        """提交实验数据"""
        try:
            # 获取实验类型
            experiment_type = self.get_experiment_type_by_code(experiment_code)
            if not experiment_type:
                raise ValueError(f"实验类型不存在: {experiment_code}")
            
            # 检查学生是否存在
            student = self.db.query(Student).filter(Student.id == student_id).first()
            if not student:
                raise ValueError(f"学生不存在: {student_id}")
            
            # 创建实验记录
            record = ExperimentRecord(
                student_id=student_id,
                experiment_type_id=experiment_type.id,
                submission_data=submission_data,
                submitted_at=datetime.utcnow(),
                status=ExperimentStatus.SUBMITTED
            )
            
            # 生成图形
            try:
                plot_data = self.plot_service.generate_plot(experiment_code, submission_data)
                record.plot_data = plot_data
            except Exception as e:
                logger.warning(f"生成图形失败: {e}")
            
            # AI分析
            try:
                analysis_result, is_passed = self.ai_service.analyze_experiment(
                    experiment_code, submission_data
                )
                record.analysis_result = analysis_result
                record.is_passed = is_passed
            except Exception as e:
                logger.warning(f"AI分析失败: {e}")
            
            # 保存到数据库
            self.db.add(record)
            self.db.commit()
            self.db.refresh(record)
            
            logger.info(f"实验提交成功: 学生ID={student_id}, 实验={experiment_code}, 记录ID={record.id}")
            
            return record
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"提交实验失败: {e}")
            raise
    
    def get_student_records(
        self,
        student_id: int,
        experiment_code: Optional[str] = None
    ) -> List[ExperimentRecord]:
        """获取学生的实验记录"""
        query = self.db.query(ExperimentRecord).filter(
            ExperimentRecord.student_id == student_id
        )
        
        if experiment_code:
            experiment_type = self.get_experiment_type_by_code(experiment_code)
            if experiment_type:
                query = query.filter(ExperimentRecord.experiment_type_id == experiment_type.id)
        
        return query.order_by(ExperimentRecord.submitted_at.desc()).all()
    
    def get_all_records(
        self,
        experiment_code: Optional[str] = None
    ) -> List[ExperimentRecord]:
        """获取所有实验记录"""
        query = self.db.query(ExperimentRecord)
        
        if experiment_code:
            experiment_type = self.get_experiment_type_by_code(experiment_code)
            if experiment_type:
                query = query.filter(ExperimentRecord.experiment_type_id == experiment_type.id)
        
        return query.order_by(ExperimentRecord.submitted_at.desc()).all()
    
    def get_record_by_id(self, record_id: int) -> Optional[ExperimentRecord]:
        """根据ID获取实验记录"""
        return self.db.query(ExperimentRecord).filter(ExperimentRecord.id == record_id).first()
    
    def update_record_review(
        self,
        record_id: int,
        reviewer_id: int,
        is_passed: bool,
        score: Optional[float] = None,
        comments: Optional[str] = None
    ) -> ExperimentRecord:
        """更新实验记录的审核信息"""
        try:
            record = self.get_record_by_id(record_id)
            if not record:
                raise ValueError(f"实验记录不存在: {record_id}")
            
            record.reviewed_by = reviewer_id
            record.reviewed_at = datetime.utcnow()
            record.is_passed = is_passed
            record.score = score
            record.review_comments = comments
            record.status = ExperimentStatus.APPROVED if is_passed else ExperimentStatus.REJECTED
            
            self.db.commit()
            self.db.refresh(record)
            
            logger.info(f"实验记录审核完成: 记录ID={record_id}, 审核人ID={reviewer_id}, 通过={is_passed}")
            
            return record
            
        except Exception as e:
            self.db.rollback()
            logger.error(f"更新实验记录审核失败: {e}")
            raise

    def submit_experiment_simple(
        self,
        student_id: str,
        student_name: str,
        experiment_code: str,
        submission_data: Dict[str, Any]
    ) -> Dict[str, Any]:
        """简化的实验提交（不需要认证）"""
        try:
            # 获取实验类型
            experiment_type = self.get_experiment_type_by_code(experiment_code)
            if not experiment_type:
                raise ValueError(f"实验类型 {experiment_code} 不存在")

            # 检查学生是否存在于正式学生表中
            existing_student = self.db.query(Student).filter(Student.student_id == student_id).first()
            student_db_id = None
            is_unassigned = False

            if existing_student:
                student_db_id = existing_student.id
                # 检查姓名是否匹配
                if existing_student.name != student_name:
                    logger.warning(f"学号 {student_id} 的姓名不匹配: 数据库中为 {existing_student.name}, 提交的为 {student_name}")
            else:
                # 学生不存在，检查是否已在未分班表中
                unassigned_student = self.db.query(UnassignedStudent).filter(
                    UnassignedStudent.student_id == student_id
                ).first()

                if not unassigned_student:
                    # 添加到未分班学生表，确保中文编码正确
                    try:
                        # 确保字符串是正确的UTF-8编码
                        safe_name = student_name
                        if isinstance(student_name, str):
                            safe_name = student_name.encode('utf-8').decode('utf-8')

                        unassigned_student = UnassignedStudent(
                            student_id=student_id,
                            name=safe_name,
                            is_processed=False,
                            notes=f"通过实验提交自动添加 - {experiment_code}"
                        )
                    except UnicodeError as e:
                        logger.error(f"字符编码错误: {e}, 原始姓名: {repr(student_name)}")
                        # 使用安全的替代方案
                        unassigned_student = UnassignedStudent(
                            student_id=student_id,
                            name=f"学生_{student_id}",
                            is_processed=False,
                            notes=f"字符编码问题，原始姓名: {repr(student_name)} - {experiment_code}"
                        )
                    self.db.add(unassigned_student)
                    self.db.flush()  # 获取ID但不提交事务
                    logger.info(f"新学生 {student_name}({student_id}) 已添加到未分班学生表")

                is_unassigned = True

            # 生成图形
            plot_data = None
            plot_json = None
            try:
                plot_result = self.plot_service.generate_plot(experiment_code, submission_data)
                if isinstance(plot_result, dict):
                    plot_data = plot_result.get('image_base64')
                    plot_json = plot_result.get('plotly_json')
                else:
                    plot_data = plot_result
            except Exception as e:
                logger.warning(f"生成图形失败: {e}")

            # AI分析
            analysis_result = "数据已提交"
            is_passed = True
            try:
                analysis = self.ai_service.analyze_experiment_data(experiment_code, submission_data)
                analysis_result = analysis.get("message", "分析完成")
                is_passed = analysis.get("is_passed", True)
            except Exception as e:
                logger.warning(f"AI分析失败: {e}")

            # 创建实验记录
            record = ExperimentRecord(
                student_id=student_db_id,  # 如果是未分班学生则为None
                experiment_type_id=experiment_type.id,
                submission_data=json.dumps(submission_data, ensure_ascii=False),
                plot_data=plot_data,
                analysis_result=analysis_result,
                is_passed=is_passed,
                status=ExperimentStatus.SUBMITTED,
                submitted_at=datetime.utcnow(),
                score=experiment_type.max_score if is_passed else 0
            )

            # 添加学号和姓名到submission_data中
            submission_data_with_info = submission_data.copy()
            submission_data_with_info['student_id'] = student_id
            submission_data_with_info['student_name'] = student_name
            record.submission_data = json.dumps(submission_data_with_info, ensure_ascii=False)

            self.db.add(record)
            self.db.commit()
            self.db.refresh(record)

            logger.info(f"学生 {student_name}({student_id}) 成功提交实验 {experiment_code}")

            # 返回详细信息
            return {
                'record': record,
                'is_unassigned': is_unassigned,
                'plot_data': plot_data,
                'plot_json': plot_json,
                'analysis_result': analysis_result,
                'is_passed': is_passed
            }

        except Exception as e:
            self.db.rollback()
            logger.error(f"提交实验失败: {e}")
            raise e
