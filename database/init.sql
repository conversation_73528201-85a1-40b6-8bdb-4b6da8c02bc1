-- 大学物理实验基础指导平台数据库初始化脚本

-- 创建数据库
CREATE DATABASE IF NOT EXISTS physics_experiments CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE physics_experiments;

-- 班级表
CREATE TABLE classes (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '班级名称',
    code VARCHAR(50) UNIQUE NOT NULL COMMENT '班级代码',
    department VARCHAR(100) COMMENT '院系',
    grade YEAR COMMENT '年级',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '班级信息表';

-- 分组表
CREATE TABLE `groups` (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '分组名称',
    class_id INT NOT NULL COMMENT '所属班级ID',
    description TEXT COMMENT '分组描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE
) COMMENT '实验分组表';

-- 学生表
CREATE TABLE students (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id VARCHAR(20) UNIQUE NOT NULL COMMENT '学号',
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    class_id INT NOT NULL COMMENT '班级ID',
    group_id INT COMMENT '分组ID',
    email VARCHAR(100) COMMENT '邮箱',
    phone VARCHAR(20) COMMENT '电话',
    password_hash VARCHAR(255) COMMENT '密码哈希',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (class_id) REFERENCES classes(id) ON DELETE CASCADE,
    FOREIGN KEY (group_id) REFERENCES `groups`(id) ON DELETE SET NULL
) COMMENT '学生信息表';

-- 管理员表
CREATE TABLE admins (
    id INT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL COMMENT '用户名',
    name VARCHAR(50) NOT NULL COMMENT '姓名',
    email VARCHAR(100) COMMENT '邮箱',
    password_hash VARCHAR(255) NOT NULL COMMENT '密码哈希',
    role ENUM('super_admin', 'admin', 'teacher') DEFAULT 'teacher' COMMENT '角色',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否激活',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '管理员表';

-- 实验类型表
CREATE TABLE experiment_types (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL COMMENT '实验名称',
    code VARCHAR(50) UNIQUE NOT NULL COMMENT '实验代码',
    description TEXT COMMENT '实验描述',
    instructions TEXT COMMENT '实验说明',
    duration_minutes INT DEFAULT 120 COMMENT '预计实验时长(分钟)',
    max_score DECIMAL(5,2) DEFAULT 100.00 COMMENT '满分',
    is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '实验类型表';

-- 实验记录表
CREATE TABLE experiment_records (
    id INT PRIMARY KEY AUTO_INCREMENT,
    student_id INT NOT NULL COMMENT '学生ID',
    experiment_type_id INT NOT NULL COMMENT '实验类型ID',
    submission_data JSON NOT NULL COMMENT '提交的实验数据',
    plot_data LONGTEXT COMMENT '图形数据(base64)',
    analysis_result TEXT COMMENT 'AI分析结果',
    is_passed BOOLEAN COMMENT '是否通过',
    score DECIMAL(5,2) COMMENT '得分',
    submitted_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '提交时间',
    reviewed_at TIMESTAMP NULL COMMENT '审核时间',
    reviewed_by INT COMMENT '审核人ID',
    review_comments TEXT COMMENT '审核意见',
    status ENUM('submitted', 'reviewed', 'approved', 'rejected') DEFAULT 'submitted' COMMENT '状态',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (student_id) REFERENCES students(id) ON DELETE CASCADE,
    FOREIGN KEY (experiment_type_id) REFERENCES experiment_types(id) ON DELETE CASCADE,
    FOREIGN KEY (reviewed_by) REFERENCES admins(id) ON DELETE SET NULL
) COMMENT '实验记录表';

-- 系统配置表
CREATE TABLE system_configs (
    id INT PRIMARY KEY AUTO_INCREMENT,
    config_key VARCHAR(100) UNIQUE NOT NULL COMMENT '配置键',
    config_value TEXT COMMENT '配置值',
    description VARCHAR(255) COMMENT '配置描述',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
) COMMENT '系统配置表';

-- 插入初始数据

-- 插入实验类型
INSERT INTO experiment_types (name, code, description, instructions) VALUES
('制流电路实验', 'current_control_circuit', '学习制流电路的工作原理和特性', '通过测量不同k值下的电流变化，分析制流电路的特性'),
('示波器实验', 'oscilloscope', '学习示波器的使用方法和波形分析', '使用示波器观察和测量各种电信号波形');

-- 插入默认管理员账户 (密码: admin123)
INSERT INTO admins (username, name, email, password_hash, role) VALUES
('admin', '系统管理员', '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3L6W.2pW2u', 'super_admin');

-- 插入示例班级
INSERT INTO classes (name, code, department, grade) VALUES
('物理学2023-1班', 'PHY2023-1', '物理学院', 2023),
('物理学2023-2班', 'PHY2023-2', '物理学院', 2023);

-- 插入示例分组
INSERT INTO `groups` (name, class_id, description) VALUES
('第一组', 1, '物理学2023-1班第一实验组'),
('第二组', 1, '物理学2023-1班第二实验组'),
('第一组', 2, '物理学2023-2班第一实验组'),
('第二组', 2, '物理学2023-2班第二实验组');

-- 插入示例学生 (密码: student123)
INSERT INTO students (student_id, name, class_id, group_id, email, password_hash) VALUES
('2023001001', '张三', 1, 1, '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3L6W.2pW2u'),
('2023001002', '李四', 1, 1, '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3L6W.2pW2u'),
('2023001003', '王五', 1, 2, '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3L6W.2pW2u'),
('2023002001', '赵六', 2, 3, '<EMAIL>', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewdBPj3L6W.2pW2u');

-- 插入系统配置
INSERT INTO system_configs (config_key, config_value, description) VALUES
('ai_analysis_enabled', 'true', '是否启用AI分析功能'),
('max_submission_attempts', '3', '每个实验最大提交次数'),
('plot_image_max_size', '5242880', '图片最大大小(字节)'),
('session_timeout_minutes', '120', '会话超时时间(分钟)');

-- 创建索引
CREATE INDEX idx_students_student_id ON students(student_id);
CREATE INDEX idx_students_class_id ON students(class_id);
CREATE INDEX idx_experiment_records_student_id ON experiment_records(student_id);
CREATE INDEX idx_experiment_records_experiment_type_id ON experiment_records(experiment_type_id);
CREATE INDEX idx_experiment_records_submitted_at ON experiment_records(submitted_at);
CREATE INDEX idx_experiment_records_status ON experiment_records(status);
