# 大学物理实验基础指导Web项目

## 项目概述

这是一个前后端分离的大学物理实验指导平台，支持多种物理实验的在线指导、数据收集、图形绘制和AI分析。

## 技术栈

### 前端
- Vue 3 + TypeScript
- Vite 构建工具
- Vue Router 路由管理
- Pinia 状态管理
- Element Plus UI组件库

### 后端
- FastAPI (Python)
- MySQL 数据库
- SQLAlchemy ORM
- Plotly 图形绘制
- JWT 认证

### 管理界面
- Vue 3 + TypeScript
- 独立的管理员界面

## 项目结构

```
physics-experiments/
├── frontend/              # Vue3+TS 学生端前端
│   ├── src/
│   │   ├── components/    # 通用组件
│   │   ├── views/         # 页面组件
│   │   ├── experiments/   # 实验相关组件
│   │   ├── api/           # API接口
│   │   ├── stores/        # Pinia状态管理
│   │   └── types/         # TypeScript类型定义
│   ├── public/
│   └── package.json
├── backend/               # FastAPI 后端
│   ├── app/
│   │   ├── api/           # API路由
│   │   ├── models/        # 数据模型
│   │   ├── services/      # 业务逻辑
│   │   ├── utils/         # 工具函数
│   │   └── main.py        # 应用入口
│   ├── requirements.txt
│   └── alembic/           # 数据库迁移
├── admin/                 # 管理员界面
│   ├── src/
│   └── package.json
├── database/              # 数据库相关
│   ├── init.sql           # 初始化脚本
│   └── migrations/        # 迁移脚本
└── docs/                  # 项目文档
```

## 支持的实验

1. **制流电路实验** - 已实现
   - 实验步骤指导
   - 数据输入与验证
   - 图形绘制
   - AI数据分析

2. **示波器实验** - 计划中
3. **其他物理实验** - 可扩展

## 功能特性

### 学生端功能
- 实验步骤指导
- 交互式数据输入
- 实时数据验证
- 图形可视化
- AI实验分析
- 实验结果提交

### 管理端功能
- 学生管理
- 班级和分组管理
- 实验监控
- 数据统计分析
- 实验结果审核

## 快速开始

### 环境要求
- Node.js 16+
- Python 3.8+
- MySQL 8.0+

### 安装步骤

1. 克隆项目
```bash
git clone <repository-url>
cd physics-experiments
```

2. 安装前端依赖
```bash
cd frontend
npm install
```

3. 安装后端依赖
```bash
cd ../backend
pip install -r requirements.txt
```

4. 配置数据库
```bash
cd ../database
mysql -u root -p < init.sql
```

5. 启动服务
```bash
# 启动后端
cd backend
uvicorn app.main:app --reload

# 启动前端
cd frontend
npm run dev
```

## 开发指南

详细的开发文档请参考 `docs/` 目录。

## 许可证

MIT License
