# Docker 端口配置说明

本项目使用 30000 起始的端口段，避免与其他应用程序冲突。

## 端口映射

| 服务 | 内部端口 | 外部端口 | 说明 |
|------|----------|----------|------|
| 数据库 (MySQL) | 3306 | 30001 | MySQL 数据库服务 |
| 后端 API | 8000 | 30000 | FastAPI 后端服务 |
| 前端应用 | 80 | 30002 | Vue.js 前端应用 |
| 管理后台 | 80 | 30003 | Vue.js 管理后台 |

## 访问地址

- **前端应用**: http://localhost:30002
- **管理后台**: http://localhost:30003
- **API 文档**: http://localhost:30000/docs
- **数据库**: localhost:30001 (用户名: root, 密码: example)

## 数据存储

- **数据库数据**: `/vdb_experiments` (主机目录)

## 环境变量配置

所有端口配置都通过根目录的 `.env` 文件进行管理：

```bash
# 端口配置 (30000 起始段)
DB_EXTERNAL_PORT=30001
API_EXTERNAL_PORT=30000
FRONTEND_EXTERNAL_PORT=30002
ADMIN_EXTERNAL_PORT=30003
```

修改端口时，只需要修改 `.env` 文件中的相应变量即可。

## 启动命令

```bash
# 启动所有服务
docker-compose up -d

# 查看服务状态
docker-compose ps

# 停止所有服务
docker-compose down

# 重新构建并启动
docker-compose up --build -d
```

## 测试服务状态

```bash
# 测试所有服务
curl -s -o /dev/null -w "%{http_code}" http://localhost:30000/docs  # 后端 API
curl -s -o /dev/null -w "%{http_code}" http://localhost:30002       # 前端
curl -s -o /dev/null -w "%{http_code}" http://localhost:30003       # 管理后台
```

## 故障排除

如果某个服务无法访问，可以：

1. 检查服务状态：`docker-compose ps`
2. 查看服务日志：`docker-compose logs [service_name]`
3. 重启特定服务：`docker-compose restart [service_name]`
4. 完全重建：`docker-compose down && docker-compose up --build -d`
docker-compose down
```

## 注意事项

1. 确保主机上的 `/vdb_experiments` 目录存在且有适当的权限
2. 如果需要修改端口，请同时更新 `.env` 文件和 `docker-compose.yml` 文件
3. 前端和管理后台都已配置为通过 nginx 代理 API 请求到后端服务
