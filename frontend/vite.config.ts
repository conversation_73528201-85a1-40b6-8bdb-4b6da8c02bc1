import { fileURLToPath, URL } from 'node:url'
import fs from 'node:fs'
import path from 'node:path'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueDevTools from 'vite-plugin-vue-devtools'

function getApiBase() {
  // Priority: explicit VITE_API_BASE -> root .env(API_EXTERNAL_PORT) -> default
  const explicit = process.env.VITE_API_BASE
  if (explicit) return explicit
  try {
    const envPath = path.resolve(__dirname, '..', '.env')
    if (fs.existsSync(envPath)) {
      const raw = fs.readFileSync(envPath, 'utf-8')
      const lines = raw.split('\n')
      const map: Record<string, string> = {}
      for (const line of lines) {
        const m = line.match(/^\s*([A-Za-z_][A-Za-z0-9_]*)\s*=\s*(.*)\s*$/)
        if (m) {
          const key = m[1]
          let val = m[2]
          if (val.startsWith('"') && val.endsWith('"')) val = val.slice(1, -1)
          if (val.startsWith("'") && val.endsWith("'")) val = val.slice(1, -1)
          // 处理变量引用，如 ${API_EXTERNAL_PORT}
          val = val.replace(/\$\{([^}]+)\}/g, (match, varName) => {
            return map[varName] || match
          })
          map[key] = val
        }
      }
      const port = map.API_EXTERNAL_PORT || map.API_PORT || '30000'
      const host = map.API_HOST || 'localhost'
      return `http://${host}:${port}`
    }
  } catch {}
  return 'http://localhost:30000'
}

const API_BASE = getApiBase()

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    vue(),
    vueDevTools(),
  ],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    },
  },
  define: {
    'import.meta.env.VITE_API_BASE': JSON.stringify(API_BASE),
  }
})
