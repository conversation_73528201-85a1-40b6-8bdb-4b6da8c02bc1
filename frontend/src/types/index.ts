/**
 * 通用类型定义
 */

// API响应基础类型
export interface ApiResponse<T = any> {
  error?: boolean
  message?: string
  status_code?: number
  data?: T
}

// 用户类型
export interface User {
  id: number
  name: string
  user_type: 'student' | 'admin'
  additional_info?: Record<string, any>
}

// 学生信息
export interface Student {
  id: number
  student_id: string
  name: string
  class_name?: string
  group_name?: string
  email?: string
  is_active: boolean
}

// 管理员信息
export interface Admin {
  id: number
  username: string
  name: string
  role: 'super_admin' | 'admin' | 'teacher'
  email?: string
  is_active: boolean
}

// 班级信息
export interface Class {
  id: number
  name: string
  code: string
  department?: string
  grade?: number
  student_count: number
}

// 实验类型
export interface ExperimentType {
  id: number
  name: string
  code: string
  description?: string
  instructions?: string
  duration_minutes: number
  max_score: number
  is_active: boolean
}

// 实验记录
export interface ExperimentRecord {
  id: number
  student_id: number
  experiment_type_id: number
  submission_data: Record<string, any>
  plot_data?: string
  analysis_result?: string
  is_passed?: boolean
  score?: number
  submitted_at: string
  reviewed_at?: string
  reviewed_by?: number
  review_comments?: string
  status: 'submitted' | 'reviewed' | 'approved' | 'rejected'
  created_at: string
  updated_at: string
}

// 登录请求
export interface LoginRequest {
  username: string
  password: string
  user_type: 'student' | 'admin'
}

// 登录响应
export interface LoginResponse {
  access_token: string
  token_type: string
  user_info: User
}

// 实验提交请求
export interface ExperimentSubmissionRequest {
  experiment_type_code: string
  submission_data: Record<string, any>
}

// 实验提交响应
export interface ExperimentSubmissionResponse {
  record_id: number
  message: string
  plot_data?: string
  analysis_result?: string
  is_passed?: boolean
}

// 图形生成请求
export interface PlotGenerationRequest {
  experiment_code: string
  data: Record<string, any>
}

// 图形生成响应
export interface PlotGenerationResponse {
  plot_data: string
  message: string
}

// 分析请求
export interface AnalysisRequest {
  experiment_code: string
  data: Record<string, any>
}

// 分析响应
export interface AnalysisResponse {
  analysis_result: string
  is_passed?: boolean
  message: string
}

// 实验统计
export interface ExperimentStatistics {
  experiment_name: string
  total_submissions: number
  passed_count: number
  failed_count: number
  pending_count: number
  pass_rate: number
}

// 制流电路实验数据
export interface CurrentControlCircuitData {
  k1_current: number[]
  k01_current: number[]
}

// 路由元信息
export interface RouteMeta {
  title?: string
  requiresAuth?: boolean
  requiresAdmin?: boolean
  requiresStudent?: boolean
}
