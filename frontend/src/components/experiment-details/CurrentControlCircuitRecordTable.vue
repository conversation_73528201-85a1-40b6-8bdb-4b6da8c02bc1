<template>
  <div class="current-control-circuit-record-table">
    <!-- 页面标题 -->
    <div class="record-header">
      <h2>
        <el-icon><DataAnalysis /></el-icon>
        制流电路实验记录表
      </h2>
      <p>实验提交数据按JSON格式展示</p>
    </div>

    <!-- 基本信息 -->
    <el-card class="basic-info-card" shadow="never">
      <template #header>
        <div class="section-header">
          <el-icon><User /></el-icon>
          <span>基本信息</span>
        </div>
      </template>
      
      <el-descriptions :column="3" border size="small">
        <el-descriptions-item label="学生姓名">
          {{ data.student_name || '未知' }}
        </el-descriptions-item>
        <el-descriptions-item label="学号">
          {{ data.student_id || '未知' }}
        </el-descriptions-item>
        <el-descriptions-item label="提交时间">
          {{ formatDateTime(record.submitted_at) }}
        </el-descriptions-item>
        <el-descriptions-item label="实验状态">
          <el-tag :type="record.is_passed ? 'success' : 'danger'" v-if="record.is_passed !== null">
            {{ record.is_passed ? '通过' : '未通过' }}
          </el-tag>
          <span v-else>待评估</span>
        </el-descriptions-item>
        <el-descriptions-item label="得分">
          {{ record.score !== null ? record.score : '未评分' }}
        </el-descriptions-item>
        <el-descriptions-item label="审核状态">
          <el-tag :type="getStatusType(record.status)" size="small">
            {{ getStatusText(record.status) }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- JSON格式的实验记录表 -->
    <el-card class="json-record-card" shadow="never">
      <template #header>
        <div class="section-header">
          <el-icon><Document /></el-icon>
          <span>实验记录表 (JSON格式)</span>
          <div class="header-actions">
            <el-button size="small" @click="copyJson">复制JSON</el-button>
            <el-button size="small" @click="downloadJson">下载JSON</el-button>
          </div>
        </div>
      </template>

      <el-collapse v-model="activeCollapse" accordion>
        <!-- 实验数据部分 -->
        <el-collapse-item name="experiment-data">
          <template #title>
            <div class="collapse-title">
              <el-icon><DataBoard /></el-icon>
              <span>实验数据：k=0.1, k=1的电流数据</span>
            </div>
          </template>
          
          <div class="data-section">
            <!-- k=1数据表格 -->
            <div class="data-group">
              <h4>k=1 电流测量数据</h4>
              <el-table :data="data.k1Data" border stripe size="small" class="data-table">
                <el-table-column prop="ratio" label="接入比例" width="120" align="center">
                  <template #default="{ row }">
                    {{ row.ratio.toFixed(1) }}
                  </template>
                </el-table-column>
                <el-table-column prop="current" label="电流值 (A)" width="150" align="center">
                  <template #default="{ row }">
                    <span v-if="row.current !== null">
                      {{ row.current?.toFixed(4) || '-' }}
                    </span>
                    <el-tag v-else type="warning" size="small">未测量</el-tag>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- k=0.1数据表格 -->
            <div class="data-group">
              <h4>k=0.1 电流测量数据</h4>
              <el-table :data="data.k01Data" border stripe size="small" class="data-table">
                <el-table-column prop="ratio" label="接入比例" width="120" align="center">
                  <template #default="{ row }">
                    {{ row.ratio.toFixed(1) }}
                  </template>
                </el-table-column>
                <el-table-column prop="current" label="电流值 (A)" width="150" align="center">
                  <template #default="{ row }">
                    <span v-if="row.current !== null">
                      {{ row.current?.toFixed(4) || '-' }}
                    </span>
                    <el-tag v-else type="warning" size="small">未测量</el-tag>
                  </template>
                </el-table-column>
              </el-table>
            </div>

            <!-- 环境条件 -->
            <div v-if="data.temperature || data.humidity" class="environment-group">
              <h4>实验环境</h4>
              <el-descriptions :column="2" size="small">
                <el-descriptions-item v-if="data.temperature" label="温度">
                  {{ data.temperature }}°C
                </el-descriptions-item>
                <el-descriptions-item v-if="data.humidity" label="湿度">
                  {{ data.humidity }}%
                </el-descriptions-item>
              </el-descriptions>
            </div>

            <!-- JSON原始数据 -->
            <div class="json-display">
              <h4>JSON数据格式</h4>
              <el-input
                v-model="experimentDataJson"
                type="textarea"
                :rows="10"
                readonly
                class="json-textarea"
              />
            </div>
          </div>
        </el-collapse-item>

        <!-- 实验图形部分 -->
        <el-collapse-item name="experiment-plot">
          <template #title>
            <div class="collapse-title">
              <el-icon><PictureRounded /></el-icon>
              <span>实验图形：图片</span>
            </div>
          </template>
          
          <div class="plot-section">
            <div v-if="plotImageUrl" class="plot-container">
              <img :src="plotImageUrl" alt="实验图形" class="plot-image" />
              <div class="plot-actions">
                <el-button size="small" @click="downloadPlot">下载图片</el-button>
                <el-button size="small" @click="viewFullImage">查看大图</el-button>
              </div>
            </div>
            <div v-else class="no-plot">
              <el-empty description="暂无实验图形" />
            </div>
          </div>
        </el-collapse-item>

        <!-- AI分析部分 -->
        <el-collapse-item name="ai-analysis">
          <template #title>
            <div class="collapse-title">
              <el-icon><ChatDotRound /></el-icon>
              <span>实验分析：AI分析</span>
            </div>
          </template>
          
          <div class="analysis-section">
            <div v-if="record.analysis_result" class="analysis-content">
              <el-alert
                :title="record.is_passed ? '实验通过' : '实验未通过'"
                :type="record.is_passed ? 'success' : 'warning'"
                :closable="false"
                show-icon
                class="analysis-alert"
              />
              <div class="analysis-text">
                {{ record.analysis_result }}
              </div>
            </div>
            <div v-else class="no-analysis">
              <el-empty description="暂无AI分析结果" />
            </div>
          </div>
        </el-collapse-item>
      </el-collapse>
    </el-card>

    <!-- 完整JSON数据预览 -->
    <el-card class="complete-json-card" shadow="never">
      <template #header>
        <div class="section-header">
          <el-icon><Tickets /></el-icon>
          <span>完整实验记录JSON</span>
        </div>
      </template>
      
      <el-input
        v-model="completeJsonData"
        type="textarea"
        :rows="15"
        readonly
        class="complete-json-textarea"
      />
    </el-card>

    <!-- 图片预览对话框 -->
    <el-dialog v-model="imageDialogVisible" title="实验图形" width="80%" center>
      <div class="image-preview">
        <img :src="plotImageUrl" alt="实验图形" class="preview-image" />
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import { ElMessage } from 'element-plus'
import { 
  User, 
  DataAnalysis, 
  PictureRounded, 
  ChatDotRound,
  Document,
  DataBoard,
  Tickets
} from '@element-plus/icons-vue'
import type { 
  CurrentControlCircuitData, 
  ExperimentRecord 
} from '@/services/experimentDataParser'

interface Props {
  data: CurrentControlCircuitData
  record: ExperimentRecord
}

const props = defineProps<Props>()

const activeCollapse = ref(['experiment-data'])
const imageDialogVisible = ref(false)

// 实验数据JSON
const experimentDataJson = computed(() => {
  const experimentData = {
    k1Data: props.data.k1Data,
    k01Data: props.data.k01Data,
    temperature: props.data.temperature,
    humidity: props.data.humidity,
    notes: props.data.notes
  }
  return JSON.stringify(experimentData, null, 2)
})

// 完整JSON数据（按用户需求格式）
const completeJsonData = computed(() => {
  const completeData = {
    "实验数据": {
      "k=1电流数据": props.data.k1Data,
      "k=0.1电流数据": props.data.k01Data,
      "实验环境": {
        "温度": props.data.temperature,
        "湿度": props.data.humidity
      },
      "备注": props.data.notes
    },
    "实验图形": props.record.plot_data ? "base64图片数据" : null,
    "实验分析": props.record.analysis_result
  }
  return JSON.stringify(completeData, null, 2)
})

// 图片URL
const plotImageUrl = computed(() => {
  if (!props.record.plot_data) return ''
  
  if (props.record.plot_data.startsWith('data:')) {
    return props.record.plot_data
  }
  
  return `data:image/png;base64,${props.record.plot_data}`
})

// 格式化日期时间
const formatDateTime = (dateString: string) => {
  return new Date(dateString).toLocaleString('zh-CN')
}

// 获取状态类型
const getStatusType = (status: string) => {
  const typeMap: Record<string, string> = {
    submitted: 'info',
    reviewed: 'warning', 
    approved: 'success',
    rejected: 'danger'
  }
  return typeMap[status] || 'info'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const textMap: Record<string, string> = {
    submitted: '已提交',
    reviewed: '已审核',
    approved: '已通过',
    rejected: '已拒绝'
  }
  return textMap[status] || status
}

// 复制JSON
const copyJson = async () => {
  try {
    await navigator.clipboard.writeText(completeJsonData.value)
    ElMessage.success('JSON数据已复制到剪贴板')
  } catch (error) {
    ElMessage.error('复制失败')
  }
}

// 下载JSON
const downloadJson = () => {
  const blob = new Blob([completeJsonData.value], { type: 'application/json' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `制流电路实验_${props.data.student_id}_${new Date().toISOString().split('T')[0]}.json`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  URL.revokeObjectURL(url)
  ElMessage.success('JSON文件下载成功')
}

// 下载图片
const downloadPlot = () => {
  if (!plotImageUrl.value) return
  
  const a = document.createElement('a')
  a.href = plotImageUrl.value
  a.download = `制流电路实验图形_${props.data.student_id}_${new Date().toISOString().split('T')[0]}.png`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  ElMessage.success('图片下载成功')
}

// 查看大图
const viewFullImage = () => {
  imageDialogVisible.value = true
}
</script>

<style scoped>
.current-control-circuit-record-table {
  padding: 20px;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.record-header {
  text-align: center;
  margin-bottom: 30px;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border-radius: 10px;
}

.record-header h2 {
  margin: 0 0 10px 0;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10px;
  font-size: 24px;
}

.record-header p {
  margin: 0;
  opacity: 0.9;
  font-size: 16px;
}

.basic-info-card,
.json-record-card,
.complete-json-card {
  margin-bottom: 20px;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  justify-content: space-between;
}

.header-actions {
  display: flex;
  gap: 8px;
}

.collapse-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: 600;
  color: #409eff;
}

.data-section {
  padding: 20px 0;
}

.data-group {
  margin-bottom: 30px;
}

.data-group h4 {
  margin: 0 0 15px 0;
  color: #606266;
  font-size: 16px;
  font-weight: 600;
  padding-bottom: 8px;
  border-bottom: 2px solid #e4e7ed;
}

.data-table {
  margin-bottom: 20px;
}

.environment-group {
  margin: 30px 0;
  padding: 20px;
  background-color: #f0f9ff;
  border-radius: 8px;
  border-left: 4px solid #409eff;
}

.environment-group h4 {
  margin: 0 0 15px 0;
  color: #409eff;
  font-size: 16px;
  font-weight: 600;
}

.json-display {
  margin-top: 30px;
}

.json-display h4 {
  margin: 0 0 15px 0;
  color: #606266;
  font-size: 16px;
  font-weight: 600;
}

.json-textarea,
.complete-json-textarea {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
  line-height: 1.5;
}

.plot-section {
  padding: 20px 0;
}

.plot-container {
  text-align: center;
}

.plot-image {
  max-width: 100%;
  max-height: 400px;
  border: 1px solid #dcdfe6;
  border-radius: 8px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
  margin-bottom: 15px;
}

.plot-actions {
  display: flex;
  justify-content: center;
  gap: 10px;
}

.no-plot,
.no-analysis {
  padding: 40px 0;
}

.analysis-section {
  padding: 20px 0;
}

.analysis-alert {
  margin-bottom: 20px;
}

.analysis-content {
  background-color: #f8f9fa;
  border-radius: 8px;
  padding: 20px;
}

.analysis-text {
  margin-top: 15px;
  line-height: 1.8;
  color: #606266;
  font-size: 14px;
  white-space: pre-wrap;
}

.image-preview {
  text-align: center;
}

.preview-image {
  max-width: 100%;
  max-height: 80vh;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

/* Element Plus 样式覆盖 */
:deep(.el-card__header) {
  background-color: #fafbfc;
  border-bottom: 1px solid #e4e7ed;
}

:deep(.el-collapse-item__header) {
  background-color: #f8f9fa;
  border-radius: 6px;
  margin-bottom: 10px;
  padding: 0 15px;
}

:deep(.el-collapse-item__content) {
  padding: 20px 15px;
  background-color: white;
  border-radius: 6px;
  border: 1px solid #e4e7ed;
}

:deep(.el-descriptions__label) {
  font-weight: 600;
  color: #606266;
}

:deep(.el-table th) {
  background-color: #f8f9fa;
  color: #606266;
  font-weight: 600;
}

:deep(.el-input__inner) {
  font-family: inherit;
}

:deep(.json-textarea .el-textarea__inner) {
  background-color: #f8f9fa;
  border: 1px solid #e4e7ed;
  color: #2c3e50;
}

:deep(.complete-json-textarea .el-textarea__inner) {
  background-color: #2c3e50;
  color: #e6db74;
  border: 1px solid #34495e;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .current-control-circuit-record-table {
    padding: 10px;
  }

  .record-header {
    padding: 15px;
  }

  .record-header h2 {
    font-size: 20px;
  }

  .section-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 10px;
  }

  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .plot-image {
    max-height: 300px;
  }
}
</style>
