<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    width="1200px"
    :close-on-click-modal="false"
    :before-close="handleClose"
    class="experiment-record-modal"
  >
    <div v-loading="loading" class="modal-content">
      <!-- 错误提示 -->
      <el-alert
        v-if="error"
        :title="error"
        type="error"
        :closable="false"
        show-icon
        class="error-alert"
      />
      
      <!-- 数据解析错误 -->
      <el-alert
        v-else-if="parsedData && !parsedData.isValid"
        title="数据解析失败"
        type="warning"
        :closable="false"
        show-icon
        class="error-alert"
      >
        <template #default>
          <div>实验数据格式有误，请检查以下问题：</div>
          <ul v-if="parsedData.errors" class="error-list">
            <li v-for="(err, index) in parsedData.errors" :key="index">{{ err }}</li>
          </ul>
        </template>
      </el-alert>
      
      <!-- 实验详情内容 -->
      <div v-else-if="parsedData && parsedData.isValid" class="experiment-detail">
        <!-- 制流电路实验 -->
        <CurrentControlCircuitDetail
          v-if="parsedData.type === 'current_control_circuit'"
          :data="parsedData.data"
          :record="record!"
        />
        
        <!-- 示波器实验（示例） -->
        <div v-else-if="parsedData.type === 'oscilloscope'" class="oscilloscope-detail">
          <el-card shadow="never">
            <template #header>
              <span>示波器实验详情</span>
            </template>
            <el-descriptions :column="2" border>
              <el-descriptions-item label="学生姓名">{{ parsedData.data.student_name }}</el-descriptions-item>
              <el-descriptions-item label="学号">{{ parsedData.data.student_id }}</el-descriptions-item>
              <el-descriptions-item label="频率">{{ parsedData.data.frequency }} Hz</el-descriptions-item>
              <el-descriptions-item label="幅度">{{ parsedData.data.amplitude }} V</el-descriptions-item>
              <el-descriptions-item label="波形类型">{{ parsedData.data.waveform_type }}</el-descriptions-item>
              <el-descriptions-item label="测量点数">{{ parsedData.data.measurements.length }}</el-descriptions-item>
            </el-descriptions>
            <div v-if="parsedData.data.notes" class="notes-section">
              <h4>实验备注</h4>
              <p>{{ parsedData.data.notes }}</p>
            </div>
          </el-card>
        </div>
        
        <!-- 未知实验类型 -->
        <div v-else class="unknown-experiment">
          <el-card shadow="never">
            <template #header>
              <span>实验记录详情</span>
            </template>
            <el-alert
              title="未知的实验类型"
              :description="`实验代码: ${record?.experiment_code || '未知'}`"
              type="warning"
              :closable="false"
              show-icon
            />
            <div class="raw-data-section">
              <h4>原始数据</h4>
              <el-input
                v-model="rawDataDisplay"
                type="textarea"
                :rows="10"
                readonly
                placeholder="无数据"
              />
            </div>
          </el-card>
        </div>
      </div>
      
      <!-- 无数据状态 -->
      <div v-else-if="!loading" class="no-data">
        <el-empty description="无实验数据" />
      </div>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleClose">关闭</el-button>
        <el-button 
          v-if="parsedData && parsedData.isValid" 
          type="primary" 
          @click="handleExport"
          :loading="exporting"
        >
          导出数据
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import { ElMessage } from 'element-plus'
import CurrentControlCircuitDetail from './experiment-details/CurrentControlCircuitDetail.vue'
import { 
  parseExperimentData, 
  type ExperimentRecord, 
  type ParsedExperimentData 
} from '@/services/experimentDataParser'

interface Props {
  visible: boolean
  record: ExperimentRecord | null
}

interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'close'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const dialogVisible = ref(false)
const loading = ref(false)
const error = ref('')
const parsedData = ref<ParsedExperimentData | null>(null)
const exporting = ref(false)

// 对话框标题
const dialogTitle = computed(() => {
  if (!props.record) return '实验记录详情'
  return `${props.record.experiment_name} - 详情`
})

// 原始数据显示（用于未知实验类型）
const rawDataDisplay = computed(() => {
  if (!props.record?.submission_data) return ''
  
  if (typeof props.record.submission_data === 'string') {
    try {
      return JSON.stringify(JSON.parse(props.record.submission_data), null, 2)
    } catch {
      return props.record.submission_data
    }
  }
  
  return JSON.stringify(props.record.submission_data, null, 2)
})

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  
  if (newVal && props.record) {
    loadExperimentData()
  } else {
    resetState()
  }
})

// 监听dialogVisible变化
watch(dialogVisible, (newVal) => {
  emit('update:visible', newVal)
})

// 加载实验数据
const loadExperimentData = async () => {
  if (!props.record) return
  
  loading.value = true
  error.value = ''
  parsedData.value = null
  
  try {
    // 解析实验数据
    const result = parseExperimentData(
      props.record.experiment_code,
      props.record.submission_data
    )
    
    parsedData.value = result
    
    if (!result.isValid && result.errors) {
      console.warn('实验数据解析警告:', result.errors)
    }
    
  } catch (err) {
    console.error('加载实验数据失败:', err)
    error.value = '加载实验数据失败: ' + (err as Error).message
  } finally {
    loading.value = false
  }
}

// 重置状态
const resetState = () => {
  loading.value = false
  error.value = ''
  parsedData.value = null
  exporting.value = false
}

// 处理关闭
const handleClose = () => {
  dialogVisible.value = false
  emit('close')
}

// 处理导出
const handleExport = async () => {
  if (!props.record || !parsedData.value) return
  
  exporting.value = true
  
  try {
    // 准备导出数据
    const exportData = {
      experiment_info: {
        id: props.record.id,
        experiment_name: props.record.experiment_name,
        experiment_code: props.record.experiment_code,
        submitted_at: props.record.submitted_at,
        is_passed: props.record.is_passed,
        score: props.record.score,
        status: props.record.status
      },
      experiment_data: parsedData.value.data,
      analysis_result: props.record.analysis_result,
      has_plot: !!props.record.plot_data
    }
    
    // 创建下载链接
    const dataStr = JSON.stringify(exportData, null, 2)
    const dataBlob = new Blob([dataStr], { type: 'application/json' })
    const url = URL.createObjectURL(dataBlob)
    
    // 创建下载链接并触发下载
    const link = document.createElement('a')
    link.href = url
    link.download = `实验记录_${props.record.id}_${new Date().toISOString().slice(0, 10)}.json`
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)
    
    // 清理URL对象
    URL.revokeObjectURL(url)
    
    ElMessage.success('数据导出成功')
    
  } catch (err) {
    console.error('导出数据失败:', err)
    ElMessage.error('导出数据失败')
  } finally {
    exporting.value = false
  }
}
</script>

<style scoped>
.experiment-record-modal {
  --el-dialog-padding-primary: 20px;
}

.modal-content {
  min-height: 200px;
}

.error-alert {
  margin-bottom: 20px;
}

.error-list {
  margin: 10px 0 0 20px;
  padding: 0;
}

.error-list li {
  margin-bottom: 5px;
  color: #e6a23c;
}

.experiment-detail {
  width: 100%;
}

.oscilloscope-detail,
.unknown-experiment {
  width: 100%;
}

.notes-section,
.raw-data-section {
  margin-top: 20px;
  padding-top: 20px;
  border-top: 1px solid #ebeef5;
}

.notes-section h4,
.raw-data-section h4 {
  margin: 0 0 15px 0;
  color: #606266;
  font-size: 14px;
  font-weight: 600;
}

.notes-section p {
  margin: 0;
  padding: 15px;
  background-color: #f8f9fa;
  border-radius: 6px;
  line-height: 1.6;
  color: #606266;
}

.no-data {
  text-align: center;
  padding: 40px 0;
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 10px;
}

:deep(.el-dialog__body) {
  padding: 20px;
  max-height: 70vh;
  overflow-y: auto;
}

:deep(.el-dialog__header) {
  padding: 20px 20px 10px 20px;
}

:deep(.el-dialog__footer) {
  padding: 10px 20px 20px 20px;
}
</style>
