<template>
  <div class="admin-records">
    <div class="records-header">
      <h2>实验记录</h2>
      <p>查看和管理学生实验提交记录</p>
    </div>

    <div class="records-toolbar">
      <el-form :inline="true" @submit.prevent>
        <el-form-item label="学号">
          <el-input v-model="filters.studentNo" placeholder="学号" clearable />
        </el-form-item>
        <el-form-item label="姓名">
          <el-input v-model="filters.studentName" placeholder="姓名" clearable />
        </el-form-item>
        <el-form-item label="实验代码">
          <el-input v-model="filters.experimentCode" placeholder="如 current_control_circuit" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" :loading="loading" @click="fetchRecords">查询</el-button>
          <el-button @click="resetFilters">重置</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="records-content">
      <el-table :data="records" v-loading="loading" style="width: 100%">
        <el-table-column prop="student_no" label="学号" width="140" />
        <el-table-column prop="student_name" label="学生姓名" width="120" />
        <el-table-column prop="experiment_name" label="实验名称" width="180" />
        <el-table-column prop="submitted_at" label="提交时间" width="200" />
        <el-table-column prop="score" label="得分" width="80" />
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusType(row.status)" size="small">
              {{ getStatusText(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="160">
          <template #default="{ row }">
            <el-button size="small" @click="viewRecord(row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="records-pagination">
        <el-pagination
          background
          layout="prev, pager, next, jumper, ->, total"
          :total="total"
          :page-size="pageSize"
          :current-page="page"
          @current-change="onPageChange"
        />
      </div>
    </div>

    <!-- 实验记录详情弹窗 -->
    <ExperimentRecordDetailModal
      v-model:visible="showDetailModal"
      :record="selectedRecord"
      @close="handleDetailModalClose"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { ElMessage } from 'element-plus'
import { adminApi } from '@/api/admin'
import ExperimentRecordDetailModal from '../ExperimentRecordDetailModal.vue'
import type { ExperimentRecord } from '@/services/experimentDataParser'

interface RecordItem {
  id: number
  student_name?: string
  student_no?: string
  experiment_name?: string
  experiment_code?: string
  submitted_at?: string
  score?: number
  status?: string
  is_passed?: boolean
  submission_data?: string | object
  plot_data?: string
  analysis_result?: string
}

const loading = ref(false)
const records = ref<RecordItem[]>([])
const total = ref(0)
const page = ref(1)
const pageSize = ref(20)

// 详情弹窗相关
const showDetailModal = ref(false)
const selectedRecord = ref<ExperimentRecord | null>(null)

const filters = reactive({
  studentNo: '',
  studentName: '',
  experimentCode: '',
})

const fetchRecords = async () => {
  loading.value = true
  try {
    const res = await adminApi.getExperimentRecords({
      page: page.value,
      page_size: pageSize.value,
      student_no: filters.studentNo || undefined,
      student_name: filters.studentName || undefined,
      experiment_code: filters.experimentCode || undefined,
    })
    const data = res as any
    records.value = data.items || []
    total.value = data.total || 0
  } catch (error) {
    ElMessage.error('获取实验记录失败')
  } finally {
    loading.value = false
  }
}

const resetFilters = () => {
  filters.studentNo = ''
  filters.studentName = ''
  filters.experimentCode = ''
  page.value = 1
  fetchRecords()
}

const onPageChange = (p: number) => {
  page.value = p
  fetchRecords()
}

const getStatusType = (status?: string) => {
  const typeMap: Record<string, string> = {
    submitted: 'warning',
    reviewed: 'success',
    approved: 'success',
    rejected: 'danger',
  }
  return (status && typeMap[status]) || 'info'
}

const getStatusText = (status?: string) => {
  const textMap: Record<string, string> = {
    submitted: '已提交',
    reviewed: '已评审',
    approved: '已通过',
    rejected: '未通过',
  }
  return (status && textMap[status]) || (status || '未知')
}

const viewRecord = (record: RecordItem) => {
  ElMessage.info(`记录 #${record.id}`)
}

onMounted(() => {
  fetchRecords()
})
</script>

<style scoped>
.admin-records {
  padding: 0;
}

.records-header {
  margin-bottom: 12px;
}

.records-header h2 {
  margin: 0 0 5px 0;
  color: #303133;
}

.records-header p {
  margin: 0;
  color: #606266;
}

.records-toolbar {
  background: white;
  border-radius: 8px;
  padding: 12px 16px;
  margin-bottom: 12px;
}

.records-content {
  background: white;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  padding: 16px;
}

.records-pagination {
  margin-top: 12px;
  display: flex;
  justify-content: flex-end;
}
</style>
